#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字形关系检查功能
验证"嗥"和"噑"的关系组检查是否正常工作
"""

import sys
import os
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from zixing_relation_analyzer import ZixingRelationAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_relation_check():
    """测试关系检查功能"""

    # 创建分析器
    analyzer = ZixingRelationAnalyzer()

    # 测试汉字
    unicode1 = "55E5"  # 嗥
    unicode2 = "5651"  # 噑

    print(f"测试汉字: 嗥({unicode1}) 和 噑({unicode2})")
    print("=" * 50)

    # 模拟API响应数据（基于您提供的关系信息）
    mock_relation_data = {
        "related_hanzi": [
            {"unicode_code": "55E5", "character": "嗥"},
            {"unicode_code": "5651", "character": "噑"},
            {"unicode_code": "734B", "character": "獋"},
            {"unicode_code": "7354", "character": "獔"}
        ],
        "zhengyi_relations": [
            {
                "source_unicode": "5651",
                "target_unicode": "55E5",
                "relation_type": "zhengyi",
                "relation_detail": "",
                "id": 86016
            }
        ],
        "fanjian_relations": []
    }

    # 1. 测试API调用
    print("1. 测试API调用...")
    relation_data1 = analyzer.get_hanzi_relation_group(unicode1)
    if relation_data1:
        print(f"✅ 成功获取 嗥({unicode1}) 的关系组数据")
        print(f"   数据键: {list(relation_data1.keys())}")
        if 'related_hanzi' in relation_data1:
            print(f"   相关汉字数量: {len(relation_data1['related_hanzi'])}")
    else:
        print(f"❌ 无法获取 嗥({unicode1}) 的关系组数据，使用模拟数据")
        relation_data1 = mock_relation_data

    relation_data2 = analyzer.get_hanzi_relation_group(unicode2)
    if relation_data2:
        print(f"✅ 成功获取 噑({unicode2}) 的关系组数据")
        print(f"   数据键: {list(relation_data2.keys())}")
        if 'related_hanzi' in relation_data2:
            print(f"   相关汉字数量: {len(relation_data2['related_hanzi'])}")
    else:
        print(f"❌ 无法获取 噑({unicode2}) 的关系组数据，使用模拟数据")
        relation_data2 = mock_relation_data
    
    print()
    
    # 2. 测试Unicode提取
    print("2. 测试Unicode提取...")
    related_unicodes1 = analyzer.extract_related_hanzi_unicodes(relation_data1)
    related_unicodes2 = analyzer.extract_related_hanzi_unicodes(relation_data2)
    
    print(f"嗥({unicode1}) 的关系组包含: {related_unicodes1}")
    print(f"噑({unicode2}) 的关系组包含: {related_unicodes2}")
    print()
    
    # 3. 测试关系检查
    print("3. 测试关系检查...")
    is_same_group_1_to_2 = analyzer.check_hanzi_in_same_group(unicode1, unicode2)
    is_same_group_2_to_1 = analyzer.check_hanzi_in_same_group(unicode2, unicode1)
    
    print(f"嗥({unicode1}) 的关系组是否包含 噑({unicode2}): {is_same_group_1_to_2}")
    print(f"噑({unicode2}) 的关系组是否包含 嗥({unicode1}): {is_same_group_2_to_1}")
    print()
    
    # 4. 检查正异关系
    print("4. 检查正异关系...")
    if 'zhengyi_relations' in relation_data1:
        zhengyi_relations1 = relation_data1['zhengyi_relations']
        print(f"嗥({unicode1}) 的正异关系: {len(zhengyi_relations1)} 个")
        for rel in zhengyi_relations1:
            print(f"   {rel.get('source_unicode')} -> {rel.get('target_unicode')} ({rel.get('relation_type')})")
    
    if 'zhengyi_relations' in relation_data2:
        zhengyi_relations2 = relation_data2['zhengyi_relations']
        print(f"噑({unicode2}) 的正异关系: {len(zhengyi_relations2)} 个")
        for rel in zhengyi_relations2:
            print(f"   {rel.get('source_unicode')} -> {rel.get('target_unicode')} ({rel.get('relation_type')})")
    
    print()
    
    # 5. 总结
    print("5. 总结")
    print("=" * 30)
    if is_same_group_1_to_2 or is_same_group_2_to_1:
        print("✅ 嗥 和 噑 在同一个关系组中")
        print("   这意味着它们应该被建立映射关系，而不是放入待检查列表")
    else:
        print("❌ 嗥 和 噑 不在同一个关系组中")
        print("   这解释了为什么它们被放入待检查列表")
    
    # 检查是否有直接的正异关系
    has_direct_relation = False
    if 'zhengyi_relations' in relation_data1:
        for rel in relation_data1['zhengyi_relations']:
            if (rel.get('source_unicode') == unicode1 and rel.get('target_unicode') == unicode2) or \
               (rel.get('source_unicode') == unicode2 and rel.get('target_unicode') == unicode1):
                has_direct_relation = True
                print(f"✅ 发现直接的正异关系: {rel.get('source_unicode')} -> {rel.get('target_unicode')}")
                break
    
    if not has_direct_relation and 'zhengyi_relations' in relation_data2:
        for rel in relation_data2['zhengyi_relations']:
            if (rel.get('source_unicode') == unicode1 and rel.get('target_unicode') == unicode2) or \
               (rel.get('source_unicode') == unicode2 and rel.get('target_unicode') == unicode1):
                has_direct_relation = True
                print(f"✅ 发现直接的正异关系: {rel.get('source_unicode')} -> {rel.get('target_unicode')}")
                break
    
    if not has_direct_relation:
        print("❌ 没有发现直接的正异关系")

if __name__ == "__main__":
    test_relation_check()
